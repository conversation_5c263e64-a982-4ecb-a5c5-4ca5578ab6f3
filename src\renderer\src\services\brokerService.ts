/**
 * Broker Service for Renderer Process
 * Handles IPC communication with the main process for broker operations
 */

import type { TradingBotConfig } from '../../../shared/types/trading'

/**
 * Response interface for IPC operations
 */
interface IPCResponse<T = unknown> {
  success: boolean
  message?: string
  error?: string
  data?: T
  config?: TradingBotConfig
}

/**
 * Type guard to check if a value is a valid IPCResponse
 */
function isIPCResponse<T = unknown>(value: unknown): value is IPCResponse<T> {
  return (
    typeof value === 'object' &&
    value !== null &&
    'success' in value &&
    typeof (value as IPCResponse).success === 'boolean'
  )
}

/**
 * Type guard to check if a value is a valid BrokerStatusData
 */
function isBrokerStatusData(value: unknown): value is BrokerStatusData {
  return (
    typeof value === 'object' &&
    value !== null &&
    'isConnected' in value &&
    'connectionState' in value &&
    'heartbeatHealth' in value &&
    'isBotActive' in value &&
    'heartbeatStats' in value &&
    typeof (value as BrokerStatusData).isConnected === 'boolean' &&
    typeof (value as BrokerStatusData).connectionState === 'string' &&
    typeof (value as BrokerStatusData).heartbeatHealth === 'string' &&
    typeof (value as BrokerStatusData).isBotActive === 'boolean'
  )
}

/**
 * Broker status data interface
 */
interface BrokerStatusData {
  isConnected: boolean
  connectionState: string
  heartbeatHealth: string
  isBotActive: boolean
  heartbeatStats: {
    health: string
    missedBeats: number
    lastSent: number
    lastReceived: number
    isActive: boolean
  }
}

/**
 * Broker Service Class
 * Provides methods to interact with the PocketOption broker through IPC
 */
export class BrokerService {
  private static instance: BrokerService | null = null

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Gets the singleton instance of BrokerService
   * @returns The singleton instance
   */
  public static getInstance(): BrokerService {
    if (!BrokerService.instance) {
      BrokerService.instance = new BrokerService()
    }
    return BrokerService.instance
  }

  /**
   * Starts the trading bot with the provided configuration
   * @param config - Trading bot configuration
   * @returns Promise resolving to operation result
   */
  public async startTradingBot(config: TradingBotConfig): Promise<IPCResponse> {
    try {
      // Validate config parameter
      if (!config || typeof config !== 'object') {
        throw new Error('Invalid trading bot configuration')
      }

      const response = await window.api.invoke<IPCResponse>('trading-bot:start', config)

      if (!isIPCResponse(response)) {
        throw new Error('Invalid response format from main process')
      }

      if (!response.success) {
        throw new Error(response.error || 'Failed to start trading bot')
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('BrokerService: Failed to start trading bot:', errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Stops the trading bot
   * @returns Promise resolving to operation result
   */
  public async stopTradingBot(): Promise<IPCResponse> {
    try {
      const response = await window.api.invoke<IPCResponse>('trading-bot:stop')

      if (!response.success) {
        throw new Error(response.error || 'Failed to stop trading bot')
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('BrokerService: Failed to stop trading bot:', errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Gets the current broker and trading bot status
   * @returns Promise resolving to status data
   */
  public async getBrokerStatus(): Promise<IPCResponse<BrokerStatusData>> {
    try {
      const response = await window.api.invoke<IPCResponse<BrokerStatusData>>('trading-bot:status')

      if (!isIPCResponse<BrokerStatusData>(response)) {
        throw new Error('Invalid response format from main process')
      }

      if (!response.success) {
        throw new Error(response.error || 'Failed to get broker status')
      }

      // Validate the data structure if present
      if (response.data && !isBrokerStatusData(response.data)) {
        throw new Error('Invalid broker status data format')
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('BrokerService: Failed to get broker status:', errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Connects the broker to PocketOption
   * @returns Promise resolving to operation result
   */
  public async connectBroker(): Promise<IPCResponse> {
    try {
      const response = await window.api.invoke<IPCResponse>('broker:connect')

      if (!response.success) {
        throw new Error(response.error || 'Failed to connect broker')
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('BrokerService: Failed to connect broker:', errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Disconnects the broker from PocketOption
   * @returns Promise resolving to operation result
   */
  public async disconnectBroker(): Promise<IPCResponse> {
    try {
      const response = await window.api.invoke<IPCResponse>('broker:disconnect')

      if (!response.success) {
        throw new Error(response.error || 'Failed to disconnect broker')
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('BrokerService: Failed to disconnect broker:', errorMessage)

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Sets up event listeners for broker events
   * @param eventHandlers - Object containing event handler functions
   */
  public setupEventListeners(eventHandlers: {
    onConnected?: () => void
    onDisconnected?: () => void
    onError?: (error: string) => void
    onBalanceUpdate?: (balance: number) => void
    onBotStarted?: (config: unknown) => void
    onBotStopped?: () => void
  }): () => void {
    const unsubscribeFunctions: (() => void)[] = []

    // Set up broker event listeners
    if (eventHandlers.onConnected) {
      const unsubscribe = window.api.on('broker:event', (event: string) => {
        if (event === 'connected' && eventHandlers.onConnected) {
          eventHandlers.onConnected()
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onDisconnected) {
      const unsubscribe = window.api.on('broker:event', (event: string) => {
        if (event === 'disconnected' && eventHandlers.onDisconnected) {
          eventHandlers.onDisconnected()
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onError) {
      const unsubscribe = window.api.on(
        'broker:event',
        (event: string, data: { error?: string }) => {
          if (event === 'error' && eventHandlers.onError && data?.error) {
            eventHandlers.onError(data.error)
          }
        }
      )
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onBalanceUpdate) {
      const unsubscribe = window.api.on('broker:event', (event: string, balance: number) => {
        if (event === 'balance' && eventHandlers.onBalanceUpdate && typeof balance === 'number') {
          eventHandlers.onBalanceUpdate(balance)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onBotStarted) {
      const unsubscribe = window.api.on('broker:event', (event: string, data: unknown) => {
        if (event === 'bot_started' && eventHandlers.onBotStarted) {
          eventHandlers.onBotStarted(data)
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    if (eventHandlers.onBotStopped) {
      const unsubscribe = window.api.on('broker:event', (event: string) => {
        if (event === 'bot_stopped' && eventHandlers.onBotStopped) {
          eventHandlers.onBotStopped()
        }
      })
      unsubscribeFunctions.push(unsubscribe)
    }

    // Return cleanup function
    return () => {
      unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
    }
  }
}

// Export singleton instance
export const brokerService = BrokerService.getInstance()
